<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Twitter Web Exporter - 扩展测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .status.success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .status.warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .test-button {
            background: #1d9bf0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #1a8cd8;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🐦 Twitter Web Exporter</h1>
        <h2>Chrome 扩展测试页面</h2>
        <p>此页面用于测试扩展的各项功能是否正常工作</p>
    </div>

    <div class="test-section">
        <h3>📋 修复状态检查</h3>
        <div id="fix-status">
            <div class="status warning">
                <strong>正在检查修复状态...</strong>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>🔧 已修复的问题</h3>
        <ul>
            <li><strong>ES6 模块导入错误</strong> - 修复了 "Cannot use import statement outside a module" 错误</li>
            <li><strong>内容安全策略 (CSP) 违规</strong> - 将内联脚本移到单独的 JS 文件</li>
            <li><strong>执行上下文错误</strong> - 确保脚本在页面上下文而不是内容脚本上下文中运行</li>
            <li><strong>数据库访问权限</strong> - 添加了必要的权限配置</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🧪 功能测试</h3>
        <button class="test-button" onclick="testPopup()">测试 Popup 功能</button>
        <button class="test-button" onclick="testDatabase()">测试数据库访问</button>
        <button class="test-button" onclick="testScriptInjection()">测试脚本注入</button>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h3>📝 使用说明</h3>
        <ol>
            <li>确保已在 Chrome 扩展管理页面加载了扩展</li>
            <li>访问 <a href="https://twitter.com" target="_blank">Twitter</a> 或 <a href="https://x.com" target="_blank">X</a></li>
            <li>检查控制台是否有错误信息</li>
            <li>测试扩展的各项功能</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🐛 故障排除</h3>
        <div class="code">
            如果仍然遇到问题，请检查：<br>
            1. 扩展是否正确加载<br>
            2. 是否在 Twitter/X 页面上<br>
            3. 浏览器控制台的错误信息<br>
            4. 扩展的权限设置
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'success') {
            const statusDiv = document.getElementById('fix-status');
            statusDiv.innerHTML = `<div class="status ${type}"><strong>${message}</strong></div>`;
        }

        function addTestResult(message, type = 'success') {
            const resultsDiv = document.getElementById('test-results');
            const resultElement = document.createElement('div');
            resultElement.className = `status ${type}`;
            resultElement.innerHTML = `<strong>${message}</strong>`;
            resultsDiv.appendChild(resultElement);
        }

        function testPopup() {
            addTestResult('Popup 测试：请检查扩展图标是否可以点击，popup 是否正常显示', 'warning');
        }

        function testDatabase() {
            if (typeof indexedDB !== 'undefined') {
                addTestResult('数据库测试：IndexedDB 可用', 'success');
            } else {
                addTestResult('数据库测试：IndexedDB 不可用', 'error');
            }
        }

        function testScriptInjection() {
            if (window.TwitterWebExporter) {
                addTestResult('脚本注入测试：TwitterWebExporter 对象已加载', 'success');
            } else {
                addTestResult('脚本注入测试：TwitterWebExporter 对象未找到，请在 Twitter/X 页面测试', 'warning');
            }
        }

        // 页面加载时检查状态
        window.addEventListener('load', function() {
            setTimeout(() => {
                updateStatus('✅ 所有修复已应用，扩展应该可以正常工作了！', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
