<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Twitter Web Exporter - 数据库功能测试</h1>
    
    <div class="test-section">
        <h2>数据库连接测试</h2>
        <button onclick="testDatabaseConnection()">测试数据库连接</button>
        <div id="connection-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>数据库计数</h2>
        <button onclick="testDatabaseCount()">获取数据库计数</button>
        <div id="count-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>缓存数据测试</h2>
        <button onclick="addTestTweetToCache()">添加测试推文到缓存</button>
        <button onclick="getCachedTweets()">获取缓存推文</button>
        <div id="cache-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>数据迁移测试</h2>
        <button onclick="testDataMigration()">测试数据迁移</button>
        <div id="migration-result" class="result"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/dexie@4.0.11/dist/dexie.min.js"></script>
    <script>
        // 模拟数据库管理器
        class TestDatabaseManager {
            constructor() {
                this.db = new Dexie('twitter-web-exporter');
                this.init();
            }

            async init() {
                try {
                    // Version 1: Original schema
                    this.db.version(1).stores({
                        tweets: 'rest_id,twe_private_fields.created_at,twe_private_fields.updated_at',
                        captures: 'id,extension,type,created_at'
                    });

                    // Version 2: Add cache table
                    this.db.version(2).stores({
                        cache: 'rest_id,twe_private_fields.created_at,twe_private_fields.updated_at',
                        tweets: 'rest_id,twe_private_fields.created_at,twe_private_fields.updated_at',
                        captures: 'id,extension,type,created_at'
                    }).upgrade(async (trans) => {
                        console.log('Upgrading database to version 2...');
                        const existingTweets = await trans.table('tweets').toArray();
                        if (existingTweets.length > 0) {
                            await trans.table('cache').bulkAdd(existingTweets);
                            await trans.table('tweets').clear();
                            console.log(`Migrated ${existingTweets.length} tweets to cache`);
                        }
                    });

                    await this.db.open();
                    console.log('Database connected successfully');
                } catch (error) {
                    console.error('Database initialization error:', error);
                }
            }

            async count() {
                try {
                    return {
                        cache: await this.db.table('cache').count(),
                        tweets: await this.db.table('tweets').count(),
                        captures: await this.db.table('captures').count()
                    };
                } catch (error) {
                    console.error('Count error:', error);
                    return null;
                }
            }

            async addToCache(tweets) {
                try {
                    return await this.db.table('cache').bulkPut(tweets);
                } catch (error) {
                    console.error('Add to cache error:', error);
                }
            }

            async getCachedTweets() {
                try {
                    return await this.db.table('cache').orderBy('twe_private_fields.created_at').reverse().toArray();
                } catch (error) {
                    console.error('Get cached tweets error:', error);
                    return [];
                }
            }

            async moveFromCacheToTweets(tweetIds) {
                try {
                    const cachedTweets = await this.db.table('cache').where('rest_id').anyOf(tweetIds).toArray();
                    if (cachedTweets.length > 0) {
                        await this.db.table('tweets').bulkPut(cachedTweets);
                        await this.db.table('cache').where('rest_id').anyOf(tweetIds).delete();
                        console.log(`Moved ${cachedTweets.length} tweets from cache to tweets`);
                    }
                } catch (error) {
                    console.error('Move tweets error:', error);
                }
            }
        }

        // 初始化数据库
        const db = new TestDatabaseManager();

        // 测试函数
        async function testDatabaseConnection() {
            const result = document.getElementById('connection-result');
            try {
                result.textContent = '正在连接数据库...';
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待初始化
                result.textContent = '✅ 数据库连接成功！';
            } catch (error) {
                result.textContent = `❌ 数据库连接失败: ${error.message}`;
            }
        }

        async function testDatabaseCount() {
            const result = document.getElementById('count-result');
            try {
                result.textContent = '正在获取数据库计数...';
                const counts = await db.count();
                result.textContent = `数据库计数:
缓存推文: ${counts.cache}
已保存推文: ${counts.tweets}
捕获记录: ${counts.captures}`;
            } catch (error) {
                result.textContent = `❌ 获取计数失败: ${error.message}`;
            }
        }

        async function addTestTweetToCache() {
            const result = document.getElementById('cache-result');
            try {
                const testTweet = {
                    rest_id: `test_${Date.now()}`,
                    legacy: {
                        full_text: '这是一条测试推文 #测试',
                        created_at: new Date().toISOString(),
                        favorited: false,
                        bookmarked: false,
                        favorite_count: Math.floor(Math.random() * 100),
                        retweet_count: Math.floor(Math.random() * 50),
                        reply_count: Math.floor(Math.random() * 20)
                    },
                    core: {
                        user_results: {
                            result: {
                                legacy: {
                                    name: '测试用户',
                                    screen_name: 'test_user',
                                    profile_image_url_https: 'https://via.placeholder.com/48'
                                }
                            }
                        }
                    },
                    twe_private_fields: {
                        created_at: Date.now(),
                        updated_at: Date.now(),
                        media_count: 0
                    }
                };

                await db.addToCache([testTweet]);
                result.textContent = `✅ 成功添加测试推文到缓存: ${testTweet.rest_id}`;
            } catch (error) {
                result.textContent = `❌ 添加失败: ${error.message}`;
            }
        }

        async function getCachedTweets() {
            const result = document.getElementById('cache-result');
            try {
                result.textContent = '正在获取缓存推文...';
                const tweets = await db.getCachedTweets();
                result.textContent = `缓存中的推文 (${tweets.length} 条):
${tweets.map(t => `- ${t.rest_id}: ${t.legacy.full_text.substring(0, 50)}...`).join('\n')}`;
            } catch (error) {
                result.textContent = `❌ 获取失败: ${error.message}`;
            }
        }

        async function testDataMigration() {
            const result = document.getElementById('migration-result');
            try {
                result.textContent = '正在测试数据迁移...';
                
                // 获取缓存中的推文
                const cachedTweets = await db.getCachedTweets();
                if (cachedTweets.length === 0) {
                    result.textContent = '❌ 缓存中没有推文可以迁移，请先添加测试推文';
                    return;
                }

                // 迁移第一条推文
                const tweetToMigrate = cachedTweets[0];
                await db.moveFromCacheToTweets([tweetToMigrate.rest_id]);
                
                const counts = await db.count();
                result.textContent = `✅ 数据迁移测试完成
迁移推文: ${tweetToMigrate.rest_id}
当前缓存: ${counts.cache} 条
已保存: ${counts.tweets} 条`;
            } catch (error) {
                result.textContent = `❌ 迁移失败: ${error.message}`;
            }
        }

        // 页面加载完成后自动测试连接
        window.addEventListener('load', () => {
            setTimeout(testDatabaseConnection, 500);
        });
    </script>
</body>
</html>
