/**
 * Content script for Chrome extension
 * This script runs in the content context and injects the main script into the page context
 */

function injectScript() {
  // Inject CSS
  const link = document.createElement('link');
  link.rel = 'stylesheet';
  link.href = chrome.runtime.getURL('injected-script.css');
  (document.head || document.documentElement).appendChild(link);

  // Inject JavaScript
  const script = document.createElement('script');
  script.src = chrome.runtime.getURL('injected-script.js');
  script.onload = function() {
    // Remove the script element after loading
    script.remove();
  };

  // Inject into the page context
  (document.head || document.documentElement).appendChild(script);
}

// Inject the script as early as possible
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', injectScript);
} else {
  injectScript();
}
