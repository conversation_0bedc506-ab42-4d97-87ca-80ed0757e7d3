# Twitter GraphQL 拦截功能

## 功能概述

本扩展新增了强大的 Twitter GraphQL 请求拦截功能，能够自动捕获和展示用户在 Twitter 上的推文互动。

## 主要特性

### 1. 双重拦截机制
- **Fetch 拦截**：拦截现代浏览器的 fetch API 请求
- **XMLHttpRequest 拦截**：拦截传统的 XHR 请求
- 确保覆盖所有可能的网络请求方式

### 2. 精准端点识别
自动识别并拦截以下 Twitter GraphQL 端点：
- `TweetDetail` - 推文详情页
- `ModeratedTimeline` - 受限推文时间线
- `UserTweets` - 用户推文列表
- `SearchTimeline` - 搜索时间线
- `HomeTimeline` - 主页时间线
- `HomeLatestTimeline` - 主页最新时间线
- `Bookmarks` - 书签列表
- `Likes` - 点赞列表
- `ListLatestTweetsTimeline` - 列表时间线
- `UserMedia` - 用户媒体

### 3. 智能数据提取
从 GraphQL 响应中提取关键字段：
- `tweet_id` - 推文ID
- `user_results.image_url` - 用户头像
- `user_results.created_at` - 用户创建时间
- `user_results.name` - 用户名称
- `user_results.screen_name` - 用户昵称
- `legacy.bookmarked` - 书签状态
- `legacy.media_url_https` - 媒体URL
- `legacy.favorited` - 点赞状态
- `legacy.full_text` - 推文全文

### 4. 数据库架构
- **cache 表**：存储所有拦截到的推文数据
- **tweets 表**：存储用户主动保存的推文
- 自动数据迁移：将原有 tweets 表数据迁移到 cache 表

### 5. 用户行为监听
监听用户的互动行为：
- **点赞操作**：`FavoriteTweet` API 调用
- **书签操作**：`CreateBookmark` API 调用
- 自动将相关推文从 cache 移动到 tweets 表

### 6. 美观的瀑布流展示
- **新标签页**：Chrome 扩展新标签页展示推文
- **响应式设计**：支持移动端、平板、桌面多种屏幕
- **瀑布流布局**：自动调整列数（1-4列）
- **虚拟滚动**：支持大量数据的流畅滚动
- **推文卡片**：包含用户信息、内容、媒体、互动数据

## 技术实现

### 核心组件

1. **GraphQLInterceptor** (`src/core/graphql-interceptor.ts`)
   - 双重拦截器实现
   - 端点识别和数据提取
   - 用户行为监听

2. **DatabaseManager** (`src/core/database/manager.ts`)
   - 数据库版本管理
   - cache 和 tweets 表操作
   - 数据迁移逻辑

3. **TweetCard** (`src/components/tweet-card.tsx`)
   - 推文卡片组件
   - 媒体展示
   - 互动数据显示

4. **TweetWaterfall** (`src/components/tweet-waterfall.tsx`)
   - 瀑布流布局
   - 响应式设计
   - 虚拟滚动

5. **TweetGallery** (`src/components/tweet-gallery.tsx`)
   - 主展示页面
   - 数据切换（已保存/缓存）
   - 推文详情模态框

### 数据流程

```
Twitter GraphQL API
        ↓
双重拦截器 (Fetch + XHR)
        ↓
数据提取和验证
        ↓
存储到 cache 表
        ↓
用户点赞/书签 ← 行为监听
        ↓
移动到 tweets 表
        ↓
新标签页展示
```

## 使用方法

1. **安装扩展**：加载 dist 目录到 Chrome 扩展
2. **浏览 Twitter**：正常浏览推文，数据自动缓存
3. **互动推文**：点赞或收藏感兴趣的推文
4. **查看收藏**：打开新标签页查看收藏的推文
5. **切换视图**：在"已保存"和"缓存"之间切换

## 配置选项

- 支持原有的所有配置选项
- 新增 GraphQL 拦截器的启用/禁用
- 数据库自动升级和迁移

## 兼容性

- Chrome 扩展 Manifest V3
- 支持 Twitter/X.com 域名
- 兼容现有的推文导出功能
- 保持原有模块化架构

## 开发说明

### 构建命令
```bash
npm run build:extension
```

### 开发模式
```bash
npm run dev
```

### 测试
```bash
npm test
```

## 注意事项

1. **隐私保护**：所有数据仅存储在本地 IndexedDB
2. **性能优化**：使用虚拟滚动处理大量数据
3. **错误处理**：完善的错误捕获和日志记录
4. **向后兼容**：保持与现有功能的完全兼容

## 未来计划

- [ ] 添加推文搜索功能
- [ ] 支持推文标签和分类
- [ ] 导出功能集成
- [ ] 推文统计和分析
- [ ] 自定义主题和布局
