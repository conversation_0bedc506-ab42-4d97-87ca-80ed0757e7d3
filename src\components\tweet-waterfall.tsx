import { useEffect, useState, useRef } from 'preact/hooks';
import { Tweet } from '@/types';
import { TweetCard } from './tweet-card';

interface TweetWaterfallProps {
  tweets: Tweet[];
  onTweetClick?: (tweet: Tweet) => void;
}

/**
 * 推文瀑布流组件
 */
export function TweetWaterfall({ tweets, onTweetClick }: TweetWaterfallProps) {
  const [columns, setColumns] = useState<Tweet[][]>([[], [], []]);
  const [columnCount, setColumnCount] = useState(3);
  const containerRef = useRef<HTMLDivElement>(null);

  // 响应式列数调整
  useEffect(() => {
    const updateColumnCount = () => {
      if (!containerRef.current) return;
      
      const width = containerRef.current.offsetWidth;
      let newColumnCount = 3;
      
      if (width < 768) {
        newColumnCount = 1; // 移动端单列
      } else if (width < 1024) {
        newColumnCount = 2; // 平板双列
      } else if (width < 1440) {
        newColumnCount = 3; // 桌面三列
      } else {
        newColumnCount = 4; // 大屏四列
      }
      
      if (newColumnCount !== columnCount) {
        setColumnCount(newColumnCount);
      }
    };

    updateColumnCount();
    window.addEventListener('resize', updateColumnCount);
    
    return () => {
      window.removeEventListener('resize', updateColumnCount);
    };
  }, [columnCount]);

  // 重新分配推文到各列
  useEffect(() => {
    const newColumns: Tweet[][] = Array.from({ length: columnCount }, () => []);
    
    tweets.forEach((tweet, index) => {
      const columnIndex = index % columnCount;
      if (newColumns[columnIndex]) {
        newColumns[columnIndex].push(tweet);
      }
    });
    
    setColumns(newColumns);
  }, [tweets, columnCount]);

  if (tweets.length === 0) {
    return (
      <div class="flex flex-col items-center justify-center py-16 text-gray-500">
        <svg class="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"/>
        </svg>
        <h3 class="text-lg font-medium mb-2">暂无推文</h3>
        <p class="text-sm text-center max-w-md">
          当您在Twitter上浏览推文时，它们会自动保存到缓存中。<br/>
          点赞或收藏推文后，它们会出现在这里。
        </p>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      class="tweet-waterfall w-full"
    >
      <div class={`grid gap-4 ${
        columnCount === 1 ? 'grid-cols-1' :
        columnCount === 2 ? 'grid-cols-2' :
        columnCount === 3 ? 'grid-cols-3' :
        'grid-cols-4'
      }`}>
        {columns.map((columnTweets, columnIndex) => (
          <div key={columnIndex} class="flex flex-col">
            {columnTweets.map((tweet) => (
              <TweetCard
                key={tweet.rest_id}
                tweet={tweet}
                onClick={() => onTweetClick?.(tweet)}
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * 虚拟滚动版本的瀑布流组件（用于大量数据）
 */
export function VirtualizedTweetWaterfall({ tweets, onTweetClick }: TweetWaterfallProps) {
  const [visibleTweets, setVisibleTweets] = useState<Tweet[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const TWEETS_PER_PAGE = 20;

  // 初始加载
  useEffect(() => {
    setVisibleTweets(tweets.slice(0, TWEETS_PER_PAGE));
    setPage(1);
  }, [tweets]);

  // 无限滚动
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current || loading) return;

      const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
      
      // 当滚动到底部附近时加载更多
      if (scrollTop + clientHeight >= scrollHeight - 1000) {
        loadMore();
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, [loading, page, tweets.length]);

  const loadMore = () => {
    if (loading || visibleTweets.length >= tweets.length) return;

    setLoading(true);
    
    // 模拟异步加载
    setTimeout(() => {
      const nextPage = page + 1;
      const startIndex = (nextPage - 1) * TWEETS_PER_PAGE;
      const endIndex = startIndex + TWEETS_PER_PAGE;
      const newTweets = tweets.slice(startIndex, endIndex);
      
      setVisibleTweets(prev => [...prev, ...newTweets]);
      setPage(nextPage);
      setLoading(false);
    }, 300);
  };

  return (
    <div 
      ref={containerRef}
      class="tweet-waterfall w-full h-full overflow-y-auto"
    >
      <TweetWaterfall 
        tweets={visibleTweets} 
        onTweetClick={onTweetClick}
      />
      
      {loading && (
        <div class="flex justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      )}
      
      {visibleTweets.length >= tweets.length && tweets.length > 0 && (
        <div class="text-center py-8 text-gray-500">
          <p>已显示全部 {tweets.length} 条推文</p>
        </div>
      )}
    </div>
  );
}
