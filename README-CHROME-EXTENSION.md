# Twitter Web Exporter - Chrome Extension

这是 Twitter Web Exporter 的 Chrome 扩展版本。

## 构建 Chrome 扩展

### 方法一：使用自动化脚本（推荐）

```bash
npm run build:chrome-extension
```

这个命令会：
1. 编译 TypeScript 代码
2. 使用 Vite 构建扩展版本
3. 自动整理文件结构
4. 在 `dist` 目录中生成完整的 Chrome 扩展

### 方法二：手动构建

```bash
# 构建扩展
npm run build:extension

# 手动整理文件（如果需要）
cp dist/src/popup.html dist/popup.html
rm -rf dist/src
cp -r dist/icons/* dist/
rm -rf dist/icons
```

## 安装 Chrome 扩展

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的 `dist` 目录

## 使用说明

### 扩展功能

- **Popup 界面**: 点击扩展图标可以打开 popup 界面，提供快速访问功能
- **Content Script**: 在 Twitter/X 页面自动注入，提供数据导出功能
- **权限**: 扩展只在 Twitter/X 域名下运行，确保安全性

### 与用户脚本版本的区别

| 功能 | 用户脚本版本 | Chrome 扩展版本 |
|------|-------------|----------------|
| 安装方式 | 需要 Tampermonkey/Violentmonkey | 直接安装到 Chrome |
| 菜单命令 | 通过脚本管理器菜单 | 通过扩展 popup |
| 权限管理 | 脚本管理器控制 | Chrome 扩展权限系统 |
| 更新方式 | 自动更新 | 手动更新或商店更新 |
| 兼容性 | 支持多种浏览器 | 仅支持 Chrome 系浏览器 |
| 执行上下文 | 直接在页面上下文 | 通过注入脚本到页面上下文 |

## 开发说明

### 项目结构

```
src/
├── content-script.tsx    # Content script 入口（负责注入）
├── injected-script.tsx   # 注入到页面上下文的主脚本
├── popup.html           # 扩展 popup 页面
├── core/               # 核心功能（与用户脚本共享）
├── modules/            # 数据导出模块
├── utils/platform.ts   # 平台兼容层
└── types/platform.d.ts # 平台类型定义

public/
├── manifest.json       # Chrome 扩展清单
└── icon-*.png         # 扩展图标

scripts/
└── build-extension.cjs # 构建脚本
```

### 平台兼容性

项目使用平台兼容层 (`src/utils/platform.ts`) 来处理用户脚本和 Chrome 扩展之间的差异：

- `registerMenuCommand()`: 用户脚本中注册菜单命令，扩展中为空操作
- `getGlobalWindow()`: 获取正确的全局 window 对象
- `openUrl()`: 在新标签页中打开 URL

### 执行上下文解决方案

Chrome 扩展的 content script 运行在隔离的"content"上下文中，无法直接访问页面的 XMLHttpRequest 和 Twitter 的全局变量。为了解决这个问题，我们采用了以下架构：

1. **Content Script** (`content-script.tsx`): 运行在 content 上下文，负责将主脚本注入到页面上下文
2. **Injected Script** (`injected-script.tsx`): 运行在页面上下文，包含所有核心功能，可以正常拦截 XMLHttpRequest

这种方法确保了扩展版本与用户脚本版本具有相同的功能和性能。

### 构建配置

Vite 配置支持两种构建模式：

- **默认模式**: 构建用户脚本版本（使用 vite-plugin-monkey）
- **扩展模式**: 构建 Chrome 扩展版本（`--mode extension`）

## 故障排除

### 常见问题

1. **扩展无法加载**
   - 检查 `dist` 目录是否包含所有必需文件
   - 确保 `manifest.json` 格式正确
   - 确认 `injected-script.js` 文件存在

2. **功能不工作**
   - 检查浏览器控制台是否有错误
   - 确保在 Twitter/X 页面上使用
   - 检查是否出现"Wrong execution context"错误（应该已修复）

3. **构建失败**
   - 运行 `npm install` 确保依赖已安装
   - 检查 Node.js 版本是否兼容

4. **XMLHttpRequest 拦截不工作**
   - 确保 `injected-script.js` 在 `web_accessible_resources` 中正确配置
   - 检查脚本是否成功注入到页面上下文

### 调试

1. 打开 Chrome 开发者工具
2. 在 "Sources" 标签页中找到扩展文件
3. 设置断点进行调试

## 许可证

MIT License - 与主项目相同
