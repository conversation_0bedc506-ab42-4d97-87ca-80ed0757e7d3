import { Interceptor } from '@/core/extensions';
import { db } from '@/core/database';
import {
  TimelineAddEntriesInstruction,
  TimelineAddToModuleInstruction,
  TimelineInstructions,
  TimelineTweet,
  Tweet,
  TimelinePinEntryInstruction,
} from '@/types';
import {
  extractTimelineTweet,
  isTimelineEntryConversationThread,
  isTimelineEntryTweet,
  isTimelineEntryProfileConversation,
  isTimelineEntrySearchGrid,
  isTimelineEntryProfileGrid,
} from '@/utils/api';
import logger from '@/utils/logger';

/**
 * 统一的推文拦截器配置
 */
interface TweetEndpointConfig {
  /** API端点的URL模式 */
  urlPattern: RegExp;
  /** 从响应JSON中提取instructions的函数 */
  extractInstructions: (json: any) => TimelineInstructions;
  /** 模块名称，用于日志 */
  moduleName: string;
  /** 是否支持对话线程 */
  supportsConversation?: boolean;
  /** 是否支持置顶推文 */
  supportsPinnedTweet?: boolean;
  /** 是否支持搜索网格 */
  supportsSearchGrid?: boolean;
  /** 是否支持用户资料网格 */
  supportsProfileGrid?: boolean;
  /** 是否支持用户资料对话 */
  supportsProfileConversation?: boolean;
}

/**
 * 推文端点配置列表
 */
const TWEET_ENDPOINTS: TweetEndpointConfig[] = [
  // 推文详情页
  {
    urlPattern: /\/graphql\/.+\/TweetDetail/,
    extractInstructions: (json) => json.data.threaded_conversation_with_injections_v2.instructions,
    moduleName: 'TweetDetail',
    supportsConversation: true,
  },
  // 受限推文详情页
  {
    urlPattern: /\/graphql\/.+\/ModeratedTimeline/,
    extractInstructions: (json) => json.data.tweet.result.timeline_response.timeline.instructions,
    moduleName: 'ModeratedTimeline',
  },
  // 用户推文
  {
    urlPattern: /\/graphql\/.+\/UserTweets/,
    extractInstructions: (json) => json.data.user.result.timeline.timeline.instructions,
    moduleName: 'UserTweets',
    supportsPinnedTweet: true,
    supportsProfileConversation: true,
  },
  // 用户媒体
  {
    urlPattern: /\/graphql\/.+\/UserMedia/,
    extractInstructions: (json) => json.data.user.result.timeline.timeline.instructions,
    moduleName: 'UserMedia',
    supportsProfileGrid: true,
  },
  // 搜索时间线
  {
    urlPattern: /\/graphql\/.+\/SearchTimeline/,
    extractInstructions: (json) => json.data.search_by_raw_query.search_timeline.timeline.instructions,
    moduleName: 'SearchTimeline',
    supportsSearchGrid: true,
  },
  // 点赞列表
  {
    urlPattern: /\/graphql\/.+\/Likes/,
    extractInstructions: (json) => json.data.user.result.timeline.timeline.instructions,
    moduleName: 'Likes',
  },
  // 书签列表
  {
    urlPattern: /\/graphql\/.+\/Bookmarks/,
    extractInstructions: (json) => json.data.bookmark_timeline_v2.timeline.instructions,
    moduleName: 'Bookmarks',
  },
  // 列表时间线
  {
    urlPattern: /\/graphql\/.+\/ListLatestTweetsTimeline/,
    extractInstructions: (json) => json.data.list.tweets_timeline.timeline.instructions,
    moduleName: 'ListTimeline',
  },
  // 主页时间线
  {
    urlPattern: /\/graphql\/.+\/HomeTimeline/,
    extractInstructions: (json) => json.data.home.home_timeline_urt.instructions,
    moduleName: 'HomeTimeline',
  },
];

/**
 * 统一的推文数据拦截器
 * 整合所有推文获取方式，专注于将推文成功写入tweets数据表
 */
export const UnifiedTweetInterceptor: Interceptor = (req, res, ext) => {
  // 查找匹配的端点配置
  const config = TWEET_ENDPOINTS.find(endpoint => endpoint.urlPattern.test(req.url));
  
  if (!config) {
    return;
  }

  try {
    const json = JSON.parse(res.responseText);
    const instructions = config.extractInstructions(json);
    const newData: Tweet[] = [];

    // 处理置顶推文
    if (config.supportsPinnedTweet) {
      const timelinePinEntryInstruction = instructions.find(
        (i) => i.type === 'TimelinePinEntry',
      ) as TimelinePinEntryInstruction;

      if (timelinePinEntryInstruction) {
        const tweet = extractTimelineTweet(timelinePinEntryInstruction.entry.content.itemContent);
        if (tweet) {
          newData.push(tweet);
        }
      }
    }

    // 处理主要的时间线条目
    const timelineAddEntriesInstruction = instructions.find(
      (i) => i.type === 'TimelineAddEntries',
    ) as TimelineAddEntriesInstruction<TimelineTweet>;

    const timelineAddEntriesInstructionEntries = timelineAddEntriesInstruction?.entries ?? [];

    for (const entry of timelineAddEntriesInstructionEntries) {
      // 普通推文
      if (isTimelineEntryTweet(entry)) {
        const tweet = extractTimelineTweet(entry.content.itemContent);
        if (tweet) {
          newData.push(tweet);
        }
      }

      // 对话线程（推文详情页）
      if (config.supportsConversation && isTimelineEntryConversationThread(entry)) {
        const tweetsInConversation = entry.content.items.map((i) => {
          if (i.entryId.includes('-tweet-')) {
            return extractTimelineTweet(i.item.itemContent);
          }
        });
        newData.push(...tweetsInConversation.filter((t): t is Tweet => !!t));
      }

      // 用户资料对话
      if (config.supportsProfileConversation && isTimelineEntryProfileConversation(entry)) {
        const tweetsInConversation = entry.content.items
          .map((i) => extractTimelineTweet(i.item.itemContent))
          .filter((t): t is Tweet => !!t);
        newData.push(...tweetsInConversation);
      }

      // 搜索网格
      if (config.supportsSearchGrid && isTimelineEntrySearchGrid(entry)) {
        const tweetsInSearchGrid = entry.content.items
          .map((i) => extractTimelineTweet(i.item.itemContent))
          .filter((t): t is Tweet => !!t);
        newData.push(...tweetsInSearchGrid);
      }

      // 用户资料网格
      if (config.supportsProfileGrid && isTimelineEntryProfileGrid(entry)) {
        const tweetsInProfileGrid = entry.content.items
          .map((i) => extractTimelineTweet(i.item.itemContent))
          .filter((t): t is Tweet => !!t);
        newData.push(...tweetsInProfileGrid);
      }
    }

    // 处理延迟加载的内容
    const timelineAddToModuleInstruction = instructions.find(
      (i) => i.type === 'TimelineAddToModule',
    ) as TimelineAddToModuleInstruction<TimelineTweet>;

    if (timelineAddToModuleInstruction) {
      const tweetsInModule = timelineAddToModuleInstruction.moduleItems
        .map((i) => extractTimelineTweet(i.item.itemContent))
        .filter((t): t is Tweet => !!t);
      newData.push(...tweetsInModule);
    }

    // 将捕获的推文添加到数据库
    if (newData.length > 0) {
      db.extAddTweets(ext.name, newData);
      logger.info(`${config.moduleName}: ${newData.length} tweets captured and saved to database`);
    }

  } catch (err) {
    logger.debug(req.method, req.url, res.status, res.responseText);
    logger.errorWithBanner(`${config?.moduleName || 'UnifiedTweetInterceptor'}: Failed to parse API response`, err as Error);
  }
};
