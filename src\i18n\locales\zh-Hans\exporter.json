{"ID": "ID", "Date": "日期", "Content": "内容", "Show Full Text": "显示全文", "Media": "媒体", "Screen Name": "用户名", "Profile Name": "用户昵称", "Profile Image": "用户头像", "Replying To": "回复推文", "RT Source": "转推来源", "Quote Source": "引用来源", "Media Tags": "圈人", "Favorites": "喜欢数量", "Retweets": "转推数量", "Bookmarks": "书签数量", "Quotes": "引用数量", "Replies": "回复数量", "Views": "查看次数", "Favorited": "已喜欢", "Retweeted": "已转推", "Bookmarked": "已加书签", "URL": "URL", "Actions": "操作", "Details": "查看详情", "Description": "简介", "Profile Banner": "个人资料头图", "Followers": "关注者数量", "FollowingCount": "正在关注数量", "Statuses": "推文数量", "Favourites": "喜欢数量", "Listed": "被加入列表数", "Location": "位置", "Website": "网站", "Birthdate": "出生日期", "Verified Type": "认证类型", "Blue Verified": "蓝标认证", "Following": "正在关注", "Follows You": "关注你", "Can DM": "可私信", "Protected": "受保护", "Created At": "创建时间", "Sender": "发送者", "Recipient": "接收者", "Conversation ID": "对话 ID", "Conversation Type": "对话类型", "Data": "数据", "Export captured data as JSON/HTML/CSV file. This may take a while depending on the amount of data. The exported file does not include media files such as images and videos but only the URLs.": "将捕获的数据导出为 JSON/HTML/CSV 文件。这可能需要一些时间，具体取决于数据量。导出的文件不包括图片和视频等媒体文件，只包括它们的 URL。", "Data length:": "数据长度:", "Include all metadata:": "包括所有元数据:", "Export as:": "导出为:", "No data selected.": "未选择数据。", "Cancel": "取消", "Start Export": "开始导出", "Download and save media files from captured data. This may take a while depending on the amount of data. Media that will be downloaded includes: profile images, profile banners (for users), images, videos (for tweets).": "从捕获的数据中下载并保存媒体文件。这可能需要一些时间，具体取决于数据量。将下载的媒体包括：用户的个人资料图片、个人资料头图、图片、推文中的视频。", "For more than 100 media or large files, it is recommended to copy the URLs and download them with an external download manager such as aria2.": "对于超过 100 个媒体或大文件，建议复制 URL 并使用外部下载管理器（如 aria2）下载。", "Filename template:": "文件名模板:", "Use aria2 format:": "使用 aria2 格式:", "Click for more information. Each URL will be on a new line, with its filename on the next line. This format is compatible with aria2.": "点击获取更多信息。每个 URL 将在新行中显示，其文件名在下一行。此格式与 aria2 兼容。", "Rate limit (ms):": "速率限制（毫秒）:", "Media Filter:": "媒体过滤器：", "File Name": "文件名", "Media Type": "媒体类型", "Download URL": "下载地址", "No media selected.": "未选择媒体。", "Copied!": "已复制！", "Copy URLs": "复制 URL", "The tweet ID": "推文 ID", "The username of tweet author": "推文作者的用户名", "The profile name of tweet author": "推文作者的用户昵称", "The media index in tweet (start from 0)": "推文中的媒体索引（从 0 开始）", "The order of media in tweet (1/2/3/4)": "推文中的媒体顺序（1/2/3/4）", "The post date in YYYYMMDD format": "发布日期（YYYYMMDD 格式）", "The post time in HHmmss format": "发布时间（HHmmss 格式）", "The media type (photo/video/animated_gif)": "媒体类型（photo/video/animated_gif）", "The file extension of media (jpg/png/mp4)": "媒体文件扩展名（jpg/png/mp4）", "Failed to export media. Open DevTools for more details.": "导出媒体失败。打开 DevTools 以获取更多详细信息。", "Failed to copy media URLs. Open DevTools for more details.": "复制媒体 URL 失败。打开 DevTools 以获取更多详细信息。", "filter.photo": "图片", "filter.video": "视频", "filter.animated_gif": "GIF", "filter.retweet": "包括转推"}