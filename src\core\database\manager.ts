import <PERSON>ie, { KeyPaths } from 'dexie';
import { exportDB, importInto } from 'dexie-export-import';

import packageJson from '@/../package.json';
import { Capture, Tweet } from '@/types';
import { extractTweetMedia } from '@/utils/api';
import { parseTwitterDateTime } from '@/utils/common';
import logger from '@/utils/logger';
import { ExtensionType } from '../extensions';

const DB_NAME = packageJson.name;
const DB_VERSION = 2;

export class DatabaseManager {
  private db: Dexie;

  constructor() {
    this.db = new Dexie(DB_NAME);
    this.init();
  }

  /*
  |--------------------------------------------------------------------------
  | Type-Safe Table Accessors
  |--------------------------------------------------------------------------
  */

  private cache() {
    return this.db.table<Tweet>('cache');
  }

  private tweets() {
    return this.db.table<Tweet>('tweets');
  }

  private captures() {
    return this.db.table<Capture>('captures');
  }

  /*
  |--------------------------------------------------------------------------
  | Read Methods for Extensions
  |--------------------------------------------------------------------------
  */

  async extGetCaptures(extName: string) {
    return this.captures().where('extension').equals(extName).toArray().catch(this.logError);
  }

  async extGetCaptureCount(extName: string) {
    return this.captures().where('extension').equals(extName).count().catch(this.logError);
  }

  async extGetCapturedTweets(extName: string) {
    const captures = await this.extGetCaptures(extName);
    if (!captures) {
      return [];
    }
    const tweetIds = captures.map((capture) => capture.data_key);
    return this.tweets()
      .where('rest_id')
      .anyOf(tweetIds)
      .filter((t) => this.filterEmptyData(t))
      .toArray()
      .catch(this.logError);
  }



  /*
  |--------------------------------------------------------------------------
  | Write Methods for Extensions
  |--------------------------------------------------------------------------
  */

  async extAddTweets(extName: string, tweets: Tweet[]) {
    await this.upsertTweets(tweets);
    await this.upsertCaptures(
      tweets.map((tweet) => ({
        id: `${extName}-${tweet.rest_id}`,
        extension: extName,
        type: ExtensionType.TWEET,
        data_key: tweet.rest_id,
        created_at: Date.now(),
      })),
    );
  }

  /*
  |--------------------------------------------------------------------------
  | Cache Methods (for GraphQL interception)
  |--------------------------------------------------------------------------
  */

  async addToCache(tweets: Tweet[]) {
    return this.upsertCache(tweets);
  }

  async moveFromCacheToTweets(tweetIds: string[]) {
    const cachedTweets = await this.cache()
      .where('rest_id')
      .anyOf(tweetIds)
      .toArray()
      .catch(this.logError);

    if (cachedTweets && cachedTweets.length > 0) {
      // Add to tweets table
      await this.upsertTweets(cachedTweets);

      // Remove from cache
      await this.cache().where('rest_id').anyOf(tweetIds).delete().catch(this.logError);

      logger.info(`Moved ${cachedTweets.length} tweets from cache to tweets table`);
    }
  }

  async getCachedTweets() {
    return this.cache()
      .orderBy('twe_private_fields.created_at')
      .reverse()
      .toArray()
      .catch(this.logError);
  }

  async getCachedTweetCount() {
    return this.cache().count().catch(this.logError);
  }

  /*
  |--------------------------------------------------------------------------
  | Delete Methods for Extensions
  |--------------------------------------------------------------------------
  */

  async extClearCaptures(extName: string) {
    const captures = await this.extGetCaptures(extName);
    if (!captures) {
      return;
    }
    return this.captures().bulkDelete(captures.map((capture) => capture.id));
  }

  /*
  |--------------------------------------------------------------------------
  | Export and Import Methods
  |--------------------------------------------------------------------------
  */

  async export() {
    return exportDB(this.db).catch(this.logError);
  }

  async import(data: Blob) {
    return importInto(this.db, data).catch(this.logError);
  }

  async clear() {
    await this.deleteAllCaptures();
    await this.deleteAllTweets();
    logger.info('Database cleared');
  }

  async count() {
    try {
      return {
        cache: await this.cache().count(),
        tweets: await this.tweets().count(),
        captures: await this.captures().count(),
      };
    } catch (error) {
      this.logError(error);
      return null;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Common Methods
  |--------------------------------------------------------------------------
  */

  async upsertTweets(tweets: Tweet[]) {
    return this.db
      .transaction('rw', this.tweets(), () => {
        const data: Tweet[] = tweets.map((tweet) => ({
          ...tweet,
          twe_private_fields: {
            created_at: +parseTwitterDateTime(tweet.legacy.created_at),
            updated_at: Date.now(),
            media_count: extractTweetMedia(tweet).length,
          },
        }));

        return this.tweets().bulkPut(data);
      })
      .catch(this.logError);
  }

  async upsertCache(tweets: Tweet[]) {
    return this.db
      .transaction('rw', this.cache(), () => {
        const data: Tweet[] = tweets.map((tweet) => ({
          ...tweet,
          twe_private_fields: {
            created_at: +parseTwitterDateTime(tweet.legacy.created_at),
            updated_at: Date.now(),
            media_count: extractTweetMedia(tweet).length,
          },
        }));

        return this.cache().bulkPut(data);
      })
      .catch(this.logError);
  }

  async upsertCaptures(captures: Capture[]) {
    return this.db
      .transaction('rw', this.captures(), () => {
        return this.captures().bulkPut(captures).catch(this.logError);
      })
      .catch(this.logError);
  }

  async deleteAllTweets() {
    return this.tweets().clear().catch(this.logError);
  }

  async deleteAllCaptures() {
    return this.captures().clear().catch(this.logError);
  }

  private filterEmptyData(data: Tweet) {
    if (!data?.legacy) {
      logger.warn('Empty data found in DB', data);
      return false;
    }
    return true;
  }

  /*
  |--------------------------------------------------------------------------
  | Migrations
  |--------------------------------------------------------------------------
  */

  async init() {
    // Indexes for the "cache" and "tweets" tables (same structure).
    const tweetIndexPaths: KeyPaths<Tweet>[] = [
      'rest_id',
      'twe_private_fields.created_at',
      'twe_private_fields.updated_at',
      'twe_private_fields.media_count',
      'core.user_results.result.legacy.screen_name',
      'legacy.favorite_count',
      'legacy.retweet_count',
      'legacy.bookmark_count',
      'legacy.quote_count',
      'legacy.reply_count',
      'views.count',
      'legacy.favorited',
      'legacy.retweeted',
      'legacy.bookmarked',
    ];

    // Indexes for the "captures" table.
    const captureIndexPaths: KeyPaths<Capture>[] = ['id', 'extension', 'type', 'created_at'];

    // Take care of database schemas and versioning.
    // See: https://dexie.org/docs/Tutorial/Design#database-versioning
    try {
      // Version 1: Original schema with tweets table
      this.db
        .version(1)
        .stores({
          tweets: tweetIndexPaths.join(','),
          captures: captureIndexPaths.join(','),
        });

      // Version 2: Rename tweets to cache, create new tweets table
      this.db
        .version(2)
        .stores({
          cache: tweetIndexPaths.join(','),
          tweets: tweetIndexPaths.join(','),
          captures: captureIndexPaths.join(','),
        })
        .upgrade(async (trans) => {
          logger.info('Database upgrading to version 2: migrating tweets to cache');

          // Get all existing tweets
          const existingTweets = await trans.table('tweets').toArray();

          if (existingTweets.length > 0) {
            // Copy to cache table
            await trans.table('cache').bulkAdd(existingTweets);
            logger.info(`Migrated ${existingTweets.length} tweets to cache table`);

            // Clear original tweets table
            await trans.table('tweets').clear();
            logger.info('Cleared original tweets table');
          }

          logger.info('Database upgrade to version 2 completed');
        });

      await this.db.open();
      logger.info('Database connected');
    } catch (error) {
      this.logError(error);
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Loggers
  |--------------------------------------------------------------------------
  */

  logError(error: unknown) {
    logger.error(`Database Error: ${(error as Error).message}`, error);
  }
}
