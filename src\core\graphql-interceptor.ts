import { db } from '@/core/database';
import { Tweet } from '@/types';
import logger from '@/utils/logger';
import { getGlobalWindow } from '@/utils/platform';

/**
 * GraphQL端点配置
 */
interface GraphQLEndpointConfig {
  /** API端点的URL模式 */
  urlPattern: RegExp;
  /** 模块名称，用于日志 */
  moduleName: string;
}

/**
 * 用户行为端点配置
 */
interface UserActionEndpointConfig {
  /** API端点的URL模式 */
  urlPattern: RegExp;
  /** 行为类型 */
  actionType: 'favorite' | 'bookmark';
  /** 模块名称，用于日志 */
  moduleName: string;
}

/**
 * 支持的GraphQL端点列表
 */
const GRAPHQL_ENDPOINTS: GraphQLEndpointConfig[] = [
  {
    urlPattern: /\/graphql\/.+\/TweetDetail/,
    moduleName: 'TweetDetail',
  },
  {
    urlPattern: /\/graphql\/.+\/ModeratedTimeline/,
    moduleName: 'ModeratedTimeline',
  },
  {
    urlPattern: /\/graphql\/.+\/UserTweets/,
    moduleName: 'UserTweets',
  },
  {
    urlPattern: /\/graphql\/.+\/SearchTimeline/,
    moduleName: 'SearchTimeline',
  },
  {
    urlPattern: /\/graphql\/.+\/HomeTimeline/,
    moduleName: 'HomeTimeline',
  },
  {
    urlPattern: /\/graphql\/.+\/HomeLatestTimeline/,
    moduleName: 'HomeLatestTimeline',
  },
  {
    urlPattern: /\/graphql\/.+\/Bookmarks/,
    moduleName: 'Bookmarks',
  },
  {
    urlPattern: /\/graphql\/.+\/Likes/,
    moduleName: 'Likes',
  },
  {
    urlPattern: /\/graphql\/.+\/ListLatestTweetsTimeline/,
    moduleName: 'ListLatestTweetsTimeline',
  },
  {
    urlPattern: /\/graphql\/.+\/UserMedia/,
    moduleName: 'UserMedia',
  },
];

/**
 * 用户行为端点列表
 */
const USER_ACTION_ENDPOINTS: UserActionEndpointConfig[] = [
  {
    urlPattern: /\/graphql\/.+\/FavoriteTweet/,
    actionType: 'favorite',
    moduleName: 'FavoriteTweet',
  },
  {
    urlPattern: /\/graphql\/.+\/CreateBookmark/,
    actionType: 'bookmark',
    moduleName: 'CreateBookmark',
  },
];

/**
 * 从GraphQL响应中提取推文数据
 */
function extractTweetsFromGraphQLResponse(json: any): Tweet[] {
  const tweets: Tweet[] = [];
  
  try {
    // 递归搜索所有包含推文数据的对象
    function searchForTweets(obj: any, path: string = '') {
      if (!obj || typeof obj !== 'object') return;
      
      // 检查是否是推文对象
      if (obj.__typename === 'Tweet' && obj.rest_id && obj.legacy) {
        // 验证必需字段
        if (obj.legacy.full_text && obj.core?.user_results?.result) {
          tweets.push(obj as Tweet);
        }
      }
      
      // 递归搜索子对象
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          searchForTweets(obj[key], `${path}.${key}`);
        }
      }
    }
    
    searchForTweets(json);
  } catch (error) {
    logger.error('Error extracting tweets from GraphQL response:', error);
  }
  
  return tweets;
}

/**
 * 从用户行为请求中提取tweet_id
 */
function extractTweetIdFromUserAction(requestBody: string): string | null {
  try {
    const data = JSON.parse(requestBody);
    return data.variables?.tweet_id || null;
  } catch (error) {
    logger.error('Error extracting tweet_id from user action:', error);
    return null;
  }
}

/**
 * 双重GraphQL拦截器类
 */
export class GraphQLInterceptor {
  private originalFetch: typeof fetch;
  private originalXHROpen: typeof XMLHttpRequest.prototype.open;
  private originalXHRSend: typeof XMLHttpRequest.prototype.send;
  private globalObject: any;

  constructor() {
    this.globalObject = getGlobalWindow();
    this.originalFetch = this.globalObject.fetch;
    this.originalXHROpen = this.globalObject.XMLHttpRequest.prototype.open;
    this.originalXHRSend = this.globalObject.XMLHttpRequest.prototype.send;
  }

  /**
   * 安装拦截器
   */
  install() {
    this.installFetchInterceptor();
    this.installXHRInterceptor();
    logger.info('GraphQL双重拦截器已安装');
  }

  /**
   * 卸载拦截器
   */
  uninstall() {
    this.globalObject.fetch = this.originalFetch;
    this.globalObject.XMLHttpRequest.prototype.open = this.originalXHROpen;
    this.globalObject.XMLHttpRequest.prototype.send = this.originalXHRSend;
    logger.info('GraphQL双重拦截器已卸载');
  }

  /**
   * 安装Fetch拦截器
   */
  private installFetchInterceptor() {
    const self = this;
    
    this.globalObject.fetch = async function(input: RequestInfo | URL, init?: RequestInit) {
      const url = typeof input === 'string' ? input : input.toString();
      const method = init?.method || 'GET';
      
      try {
        const response = await self.originalFetch.call(this, input, init);
        
        // 拦截GraphQL响应
        if (self.shouldInterceptGraphQLResponse(url)) {
          const clonedResponse = response.clone();
          self.handleGraphQLResponse(url, method, clonedResponse);
        }
        
        // 拦截用户行为请求
        if (self.shouldInterceptUserAction(url) && init?.body) {
          self.handleUserAction(url, method, init.body.toString());
        }
        
        return response;
      } catch (error) {
        logger.error('Fetch interceptor error:', error);
        throw error;
      }
    };
  }

  /**
   * 安装XMLHttpRequest拦截器
   */
  private installXHRInterceptor() {
    const self = this;

    this.globalObject.XMLHttpRequest.prototype.open = function(method: string, url: string) {
      (this as any)._interceptedUrl = url;
      (this as any)._interceptedMethod = method;
      return self.originalXHROpen.call(this, method, url, true, null, null);
    };
    
    this.globalObject.XMLHttpRequest.prototype.send = function(body?: Document | XMLHttpRequestBodyInit | null) {
      const xhr = this;
      
      // 监听响应完成
      xhr.addEventListener('load', () => {
        try {
          const url = (xhr as any)._interceptedUrl;
          const method = (xhr as any)._interceptedMethod;
          
          // 拦截GraphQL响应
          if (self.shouldInterceptGraphQLResponse(url)) {
            self.handleGraphQLResponseText(url, method, xhr.responseText);
          }
          
          // 拦截用户行为请求
          if (self.shouldInterceptUserAction(url) && body) {
            self.handleUserAction(url, method, body.toString());
          }
        } catch (error) {
          logger.error('XHR interceptor error:', error);
        }
      });
      
      return self.originalXHRSend.call(this, body);
    };
  }

  /**
   * 检查是否应该拦截GraphQL响应
   */
  private shouldInterceptGraphQLResponse(url: string): boolean {
    return GRAPHQL_ENDPOINTS.some(endpoint => endpoint.urlPattern.test(url));
  }

  /**
   * 检查是否应该拦截用户行为
   */
  private shouldInterceptUserAction(url: string): boolean {
    return USER_ACTION_ENDPOINTS.some(endpoint => endpoint.urlPattern.test(url));
  }

  /**
   * 处理GraphQL响应 (Fetch)
   */
  private async handleGraphQLResponse(url: string, method: string, response: Response) {
    try {
      const json = await response.json();
      this.processGraphQLData(url, method, json);
    } catch (error) {
      logger.error('Error processing GraphQL fetch response:', error);
    }
  }

  /**
   * 处理GraphQL响应 (XHR)
   */
  private handleGraphQLResponseText(url: string, method: string, responseText: string) {
    try {
      const json = JSON.parse(responseText);
      this.processGraphQLData(url, method, json);
    } catch (error) {
      logger.error('Error processing GraphQL XHR response:', error);
    }
  }

  /**
   * 处理GraphQL数据
   */
  private processGraphQLData(url: string, method: string, json: any) {
    const config = GRAPHQL_ENDPOINTS.find(endpoint => endpoint.urlPattern.test(url));
    if (!config) return;

    const tweets = extractTweetsFromGraphQLResponse(json);
    
    if (tweets.length > 0) {
      db.addToCache(tweets);
      logger.info(`${config.moduleName}: ${tweets.length} tweets added to cache`);
    }
  }

  /**
   * 处理用户行为
   */
  private handleUserAction(url: string, method: string, requestBody: string) {
    const config = USER_ACTION_ENDPOINTS.find(endpoint => endpoint.urlPattern.test(url));
    if (!config) return;

    const tweetId = extractTweetIdFromUserAction(requestBody);
    
    if (tweetId) {
      db.moveFromCacheToTweets([tweetId]);
      logger.info(`${config.moduleName}: Tweet ${tweetId} moved from cache to tweets`);
    }
  }
}

// 全局实例
export const graphqlInterceptor = new GraphQLInterceptor();
