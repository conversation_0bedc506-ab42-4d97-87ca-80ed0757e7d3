<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Twitter Web Exporter</title>
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .logo {
      width: 32px;
      height: 32px;
      margin-bottom: 10px;
    }
    h1 {
      font-size: 18px;
      margin: 0;
      color: #1d9bf0;
    }
    .description {
      font-size: 14px;
      color: #536471;
      margin-bottom: 20px;
      line-height: 1.4;
    }
    .action-button {
      width: 100%;
      padding: 12px;
      background: #1d9bf0;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      margin-bottom: 10px;
      transition: background-color 0.2s;
    }
    .action-button:hover {
      background: #1a8cd8;
    }
    .secondary-button {
      background: #f7f9fa;
      color: #0f1419;
      border: 1px solid #cfd9de;
    }
    .secondary-button:hover {
      background: #e7e9ea;
    }
    .status {
      font-size: 12px;
      color: #536471;
      text-align: center;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">🐦</div>
    <h1>Twitter Web Exporter</h1>
  </div>
  
  <div class="description">
    Export tweets, bookmarks, lists and much more from Twitter(X) web app.
  </div>
  
  <button class="action-button" id="openTwitter">
    Open Twitter/X
  </button>
  
  <button class="action-button secondary-button" id="viewDocs">
    View Documentation
  </button>
  
  <div class="status" id="status">
    Navigate to Twitter/X to start exporting data
  </div>

  <script>
    document.getElementById('openTwitter').addEventListener('click', () => {
      chrome.tabs.create({ url: 'https://twitter.com' });
      window.close();
    });
    
    document.getElementById('viewDocs').addEventListener('click', () => {
      chrome.tabs.create({ url: 'https://github.com/prinsss/twitter-web-exporter' });
      window.close();
    });
    
    // Check if current tab is Twitter/X
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      const currentTab = tabs[0];
      if (currentTab && (currentTab.url.includes('twitter.com') || currentTab.url.includes('x.com'))) {
        document.getElementById('status').textContent = 'Extension is active on this page';
        document.getElementById('openTwitter').textContent = 'Refresh Page';
        document.getElementById('openTwitter').addEventListener('click', () => {
          chrome.tabs.reload(currentTab.id);
          window.close();
        });
      }
    });
  </script>
</body>
</html>
