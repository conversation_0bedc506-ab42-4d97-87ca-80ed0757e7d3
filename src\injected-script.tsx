/**
 * Injected script that runs in the page context
 * This script has access to the page's window object and can intercept XMLHttpRequest
 */

import { render } from 'preact';
import { App } from './core/app';
import extensions from './core/extensions';

import DirectMessagesModule from './modules/direct-messages';
import RuntimeLogsModule from './modules/runtime-logs';
import UnifiedTweetsModule from './modules/unified-tweets';

import './index.css';

// Register all extensions
// 使用统一的推文模块替换所有单独的推文获取模块
extensions.add(UnifiedTweetsModule);
extensions.add(DirectMessagesModule);
extensions.add(RuntimeLogsModule);
extensions.start();

function mountApp() {
  const root = document.createElement('div');
  root.id = 'twe-root';
  document.body.append(root);

  render(<App />, root);
}

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', mountApp);
} else {
  mountApp();
}
