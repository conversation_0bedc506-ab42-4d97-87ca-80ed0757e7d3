import { ExportMediaModal } from '@/components/modals/export-media';
import { useCapturedRecords, useClearCaptures } from '@/core/database/hooks';
import { Extension, ExtensionType } from '@/core/extensions';
import { useTranslation } from '@/i18n';
import { Tweet } from '@/types';
import { useToggle } from '@/utils/common';
import { ColumnDef } from '@tanstack/table-core';

import { BaseTableView } from './base';
import { columns as columnsTweet } from './columns-tweet';

type TableViewProps = {
  title: string;
  extension: Extension;
};

/**
 * Common table view.
 */
export function TableView({ title, extension }: TableViewProps) {
  const { t } = useTranslation();

  // Query records from the database.
  const { name, type } = extension;
  const records = useCapturedRecords(name, type);
  const clearCapturedData = useClearCaptures(name);

  // Control modal visibility for exporting media.
  const [showExportMediaModal, toggleShowExportMediaModal] = useToggle();

  const columns = columnsTweet as ColumnDef<Tweet>[];

  return (
    <BaseTableView
      title={title}
      records={records ?? []}
      columns={columns}
      clear={clearCapturedData}
      renderActions={() => (
        <button class="btn btn-secondary" onClick={toggleShowExportMediaModal}>
          {t('Export Media')}
        </button>
      )}
      renderExtra={(table) => (
        <ExportMediaModal
          title={title}
          table={table}
          isTweet={type === ExtensionType.TWEET}
          show={showExportMediaModal}
          onClose={toggleShowExportMediaModal}
        />
      )}
    />
  );
}
