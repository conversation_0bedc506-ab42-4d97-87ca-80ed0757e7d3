import { CommonModuleUI } from '@/components/module-ui';
import { Extension, ExtensionType } from '@/core/extensions';
import { UnifiedTweetInterceptor } from '@/core/tweet-interceptor';

/**
 * 统一的推文模块
 * 整合所有推文获取方式，专注于将推文成功写入tweets数据表
 */
export default class UnifiedTweetsModule extends Extension {
  name = 'UnifiedTweetsModule';

  type = ExtensionType.TWEET;

  intercept() {
    return UnifiedTweetInterceptor;
  }

  render() {
    return CommonModuleUI;
  }
}
