# Set default behavior to automatically normalize line endings
* text=auto eol=crlf

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout
*.ts text eol=crlf
*.tsx text eol=crlf
*.js text eol=crlf
*.jsx text eol=crlf
*.json text eol=crlf
*.css text eol=crlf
*.html text eol=crlf
*.md text eol=crlf
*.yml text eol=crlf
*.yaml text eol=crlf
*.toml text eol=crlf

# Declare files that will always have LF line endings on checkout
*.sh text eol=lf

# Denote all files that are truly binary and should not be modified
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg binary
