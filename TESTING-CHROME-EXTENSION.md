# Chrome 扩展测试指南

## 问题修复总结

我们已经成功修复了以下问题：

### 1. 执行上下文错误
**问题**: `Wrong execution context detected. This script needs to be injected into "page" context rather than "content" context.`

**解决方案**: 
- 创建了双层架构：content script + injected script
- Content script 负责将主脚本注入到页面上下文
- Injected script 在页面上下文中运行，可以正常拦截 XMLHttpRequest

### 2. CSS 加载错误
**问题**: `无法为脚本加载重叠样式表"content-script.css"`

**解决方案**:
- 移除了 manifest.json 中的 CSS 引用
- 通过 content script 动态注入 CSS 文件
- 将 CSS 文件添加到 web_accessible_resources

### 3. 调试日志过多
**问题**: 控制台出现大量 XHR 调试日志

**解决方案**:
- 修复了选项管理器的默认值加载问题
- 确保调试模式默认关闭
- 用户可以通过扩展设置手动开启调试模式

## 测试步骤

### 1. 构建扩展
```bash
npm run build:chrome-extension
```

### 2. 加载扩展
1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的 `dist` 目录

### 3. 验证加载成功
- 扩展应该出现在扩展列表中
- 没有错误提示
- 扩展图标显示在工具栏中

### 4. 功能测试
1. 访问 Twitter/X 网站 (twitter.com 或 x.com)
2. 打开浏览器开发者工具 (F12)
3. 检查控制台输出：
   - 应该看到 `[twitter-web-exporter] Hooked into XMLHttpRequest`
   - 不应该看到 "Wrong execution context" 错误
   - 不应该看到大量的 XHR 调试日志（除非手动开启调试模式）
4. 查看页面右下角是否出现扩展的控制面板
5. 点击扩展图标测试 popup 界面

### 5. 数据导出测试
1. 在 Twitter 上浏览一些内容（推文、用户资料等）
2. 打开扩展控制面板
3. 检查是否有数据被捕获
4. 尝试导出功能

## 预期结果

✅ **成功指标**:
- 扩展正常加载，无错误提示
- 控制台显示 XMLHttpRequest 已被拦截
- 扩展界面正常显示
- 数据捕获功能正常工作
- 导出功能正常工作

❌ **失败指标**:
- 扩展加载失败
- 控制台出现 "Wrong execution context" 错误
- 控制台出现 CSS 加载错误
- 扩展界面不显示
- 数据捕获不工作

## 故障排除

### 如果扩展无法加载
1. 检查 `dist` 目录是否包含所有必需文件
2. 检查 manifest.json 格式是否正确
3. 查看 Chrome 扩展页面的错误信息

### 如果功能不工作
1. 打开开发者工具查看控制台错误
2. 检查 Network 标签页是否有资源加载失败
3. 确认在正确的网站上测试 (twitter.com 或 x.com)

### 如果仍有执行上下文错误
1. 确认 injected-script.js 文件存在
2. 检查 web_accessible_resources 配置
3. 查看 content-script.js 是否正确注入脚本

## 文件结构验证

构建后的 `dist` 目录应该包含：
```
dist/
├── content-script.js      # Content script (小文件，约0.6KB)
├── injected-script.js     # 主脚本 (大文件，约721KB)
├── injected-script.css    # 样式文件 (约113KB)
├── popup.html            # 弹出页面
├── manifest.json         # 扩展清单
├── icon-16.png          # 图标文件
├── icon-32.png
├── icon-48.png
└── icon-128.png
```

如果文件结构不正确，请重新运行构建命令。
