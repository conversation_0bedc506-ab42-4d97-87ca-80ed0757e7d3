{"manifest_version": 3, "name": "Twitter Web Exporter", "description": "Export tweets, bookmarks, lists and much more from Twitter(X) web app.", "version": "1.2.2", "author": "prin <<EMAIL>>", "homepage_url": "https://github.com/prinsss/twitter-web-exporter", "permissions": ["storage", "downloads"], "host_permissions": ["*://twitter.com/*", "*://x.com/*"], "content_scripts": [{"matches": ["*://twitter.com/*", "*://x.com/*"], "js": ["content-script.js"], "run_at": "document_start"}], "web_accessible_resources": [{"resources": ["assets/*", "injected-script.js", "injected-script.css"], "matches": ["*://twitter.com/*", "*://x.com/*"]}], "icons": {"16": "icon-16.png", "32": "icon-32.png", "48": "icon-48.png", "128": "icon-128.png"}, "action": {"default_popup": "popup.html", "default_title": "Twitter Web Exporter"}, "chrome_url_overrides": {"newtab": "newtab.html"}}