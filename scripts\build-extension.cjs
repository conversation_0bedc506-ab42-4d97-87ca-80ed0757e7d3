#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔨 Building Chrome Extension...');

// Clean dist directory
if (fs.existsSync('dist')) {
  fs.rmSync('dist', { recursive: true, force: true });
}

// Build with Vite
console.log('📦 Running Vite build...');
execSync('npm run build:extension', { stdio: 'inherit' });

// Move files to correct locations
console.log('📁 Organizing files...');

// Move HTML files to root
if (fs.existsSync('dist/src/popup.html')) {
  fs.copyFileSync('dist/src/popup.html', 'dist/popup.html');
}
if (fs.existsSync('dist/src/newtab.html')) {
  fs.copyFileSync('dist/src/newtab.html', 'dist/newtab.html');
}
if (fs.existsSync('dist/src')) {
  fs.rmSync('dist/src', { recursive: true, force: true });
}

// Move icons to root
if (fs.existsSync('dist/icons')) {
  const iconFiles = fs.readdirSync('dist/icons');
  iconFiles.forEach(file => {
    fs.copyFileSync(path.join('dist/icons', file), path.join('dist', file));
  });
  fs.rmSync('dist/icons', { recursive: true, force: true });
}

// Remove content-script.css if it exists (not needed for extension)
if (fs.existsSync('dist/content-script.css')) {
  fs.rmSync('dist/content-script.css');
}

console.log('✅ Chrome Extension built successfully!');
console.log('📂 Extension files are in the "dist" directory');
console.log('🚀 You can now load the extension in Chrome:');
console.log('   1. Open Chrome and go to chrome://extensions/');
console.log('   2. Enable "Developer mode"');
console.log('   3. Click "Load unpacked" and select the "dist" directory');
