{"name": "twitter-web-exporter", "description": "Export tweets, bookmarks, lists and much more from Twitter(X) web app.", "version": "1.2.2-beta.1", "author": "prin <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/prinsss/twitter-web-exporter", "bugs": "https://github.com/prinsss/twitter-web-exporter/issues", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:extension": "tsc && vite build --mode extension", "build:chrome-extension": "node scripts/build-extension.cjs", "prepare": "husky", "lint": "eslint .", "commitlint": "commitlint --edit", "changelog": "git-cliff -o CHANGELOG.md", "preview": "vite preview"}, "dependencies": {"@preact/signals": "2.0.0", "@preact/signals-core": "1.8.0", "@tabler/icons-preact": "3.31.0", "@tanstack/table-core": "8.21.2", "dayjs": "1.11.13", "dexie": "4.0.11", "dexie-export-import": "4.1.4", "file-saver-es": "2.0.5", "i18next": "24.2.3", "preact": "10.26.4"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/js": "^9.23.0", "@preact/preset-vite": "^2.10.1", "@types/file-saver-es": "^2.0.3", "@types/node": "^22.14.0", "autoprefixer": "^10.4.21", "daisyui": "^4", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.6", "git-cliff": "^2.8.0", "husky": "^9.1.7", "postcss": "^8.5.3", "postcss-prefix-selector": "^2.1.1", "postcss-rem-to-pixel-next": "^5.0.3", "tailwindcss": "^3", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0", "vite": "^6.2.5", "vite-plugin-i18next-loader": "^3.1.2", "vite-plugin-monkey": "^5.0.8"}}