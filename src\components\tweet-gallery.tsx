import { useEffect, useState } from 'preact/hooks';
import { Tweet } from '@/types';
import { db } from '@/core/database';
import { VirtualizedTweetWaterfall } from './tweet-waterfall';

// 简化的实时查询hook
function useLiveQuery<T>(queryFn: () => Promise<T | undefined>, deps: any[], defaultValue: T): T {
  const [data, setData] = useState<T>(defaultValue);

  useEffect(() => {
    let mounted = true;

    const updateData = async () => {
      try {
        const result = await queryFn();
        if (mounted && result !== undefined) {
          setData(result);
        }
      } catch (error) {
        console.error('Live query error:', error);
      }
    };

    updateData();

    // 设置定期更新
    const interval = setInterval(updateData, 1000);

    return () => {
      mounted = false;
      clearInterval(interval);
    };
  }, deps);

  return data;
}

/**
 * 推文画廊组件 - 展示用户收藏的推文
 */
export function TweetGallery() {
  const [selectedTweet, setSelectedTweet] = useState<Tweet | null>(null);
  const [viewMode, setViewMode] = useState<'saved' | 'cache'>('saved');
  
  // 使用实时查询获取推文数据
  const savedTweets = useLiveQuery(() => db.extGetCapturedTweets('UnifiedTweets'), [], []);
  const cachedTweets = useLiveQuery(() => db.getCachedTweets(), [], []);
  const counts = useLiveQuery(() => db.count(), [], { cache: 0, tweets: 0, captures: 0 });

  const currentTweets = viewMode === 'saved' ? (savedTweets || []) : (cachedTweets || []);

  const handleTweetClick = (tweet: Tweet) => {
    setSelectedTweet(tweet);
  };

  const closeTweetModal = () => {
    setSelectedTweet(null);
  };

  return (
    <div class="tweet-gallery h-full flex flex-col bg-gray-50">
      {/* 头部导航 */}
      <div class="bg-white shadow-sm border-b px-6 py-4">
        <div class="flex items-center justify-between">
          <h1 class="text-2xl font-bold text-gray-900">推文收藏</h1>
          
          {/* 视图切换 */}
          <div class="flex items-center space-x-4">
            <div class="flex bg-gray-100 rounded-lg p-1">
              <button
                class={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'saved'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setViewMode('saved')}
              >
                已保存 ({counts?.tweets || 0})
              </button>
              <button
                class={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'cache'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setViewMode('cache')}
              >
                缓存 ({counts?.cache || 0})
              </button>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div class="mt-4 flex items-center space-x-6 text-sm text-gray-600">
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
            </svg>
            <span>已保存: {counts?.tweets || 0}</span>
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4z"/>
            </svg>
            <span>缓存: {counts?.cache || 0}</span>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div class="flex-1 overflow-hidden">
        <div class="h-full px-6 py-6">
          <VirtualizedTweetWaterfall
            tweets={currentTweets}
            onTweetClick={handleTweetClick}
          />
        </div>
      </div>

      {/* 推文详情模态框 */}
      {selectedTweet && (
        <TweetModal
          tweet={selectedTweet}
          onClose={closeTweetModal}
        />
      )}
    </div>
  );
}

/**
 * 推文详情模态框组件
 */
function TweetModal({ tweet, onClose }: { tweet: Tweet; onClose: () => void }) {
  const user = tweet.core.user_results.result;
  const legacy = tweet.legacy;
  
  const userName = user?.legacy?.name || '未知用户';
  const screenName = user?.legacy?.screen_name || 'unknown';
  const userImage = user?.legacy?.profile_image_url_https || '';
  const tweetUrl = `https://x.com/${screenName}/status/${tweet.rest_id}`;

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  return (
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* 模态框头部 */}
        <div class="flex items-center justify-between p-4 border-b">
          <div class="flex items-center">
            {userImage && (
              <img 
                src={userImage} 
                alt={userName}
                class="w-10 h-10 rounded-full mr-3"
              />
            )}
            <div>
              <h3 class="font-semibold text-gray-900">{userName}</h3>
              <p class="text-gray-500 text-sm">@{screenName}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            class="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        {/* 推文内容 */}
        <div class="p-6">
          <p class="text-gray-800 whitespace-pre-wrap break-words leading-relaxed mb-4">
            {legacy.full_text}
          </p>

          {/* 互动数据 */}
          <div class="flex items-center space-x-6 text-gray-500 text-sm mb-4">
            <span>{legacy.reply_count || 0} 回复</span>
            <span>{legacy.retweet_count || 0} 转推</span>
            <span>{legacy.favorite_count || 0} 点赞</span>
            {tweet.views?.count && (
              <span>{parseInt(tweet.views.count).toLocaleString()} 查看</span>
            )}
          </div>

          {/* 操作按钮 */}
          <div class="flex items-center space-x-4">
            <a
              href={tweetUrl}
              target="_blank"
              rel="noopener noreferrer"
              class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
              </svg>
              在Twitter中查看
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
