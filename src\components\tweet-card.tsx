import { Tweet } from '@/types';
import { formatDateTime } from '@/utils/common';
import { extractTweetMedia } from '@/utils/api';
import { options } from '@/core/options';

interface TweetCardProps {
  tweet: Tweet;
  onClick?: () => void;
}

/**
 * 推文卡片组件
 */
export function TweetCard({ tweet, onClick }: TweetCardProps) {
  const user = tweet.core.user_results.result;
  const legacy = tweet.legacy;
  const media = extractTweetMedia(tweet);
  
  // 获取用户信息
  const userName = user?.legacy?.name || '未知用户';
  const screenName = user?.legacy?.screen_name || 'unknown';
  const userImage = user?.legacy?.profile_image_url_https || '';
  
  // 格式化时间
  const createdAt = formatDateTime(
    tweet.twe_private_fields.created_at,
    options.get('dateTimeFormat')
  );
  
  // 构建推文链接
  const tweetUrl = `https://x.com/${screenName}/status/${tweet.rest_id}`;

  return (
    <div 
      class="tweet-card bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 p-4 mb-4 cursor-pointer border border-gray-200"
      onClick={onClick}
    >
      {/* 用户信息头部 */}
      <div class="flex items-center mb-3">
        {userImage && (
          <img 
            src={userImage} 
            alt={userName}
            class="w-10 h-10 rounded-full mr-3"
          />
        )}
        <div class="flex-1 min-w-0">
          <div class="flex items-center">
            <h3 class="font-semibold text-gray-900 truncate mr-2">
              {userName}
            </h3>
            <span class="text-gray-500 text-sm truncate">
              @{screenName}
            </span>
          </div>
          <a 
            href={tweetUrl}
            target="_blank"
            rel="noopener noreferrer"
            class="text-gray-500 text-sm hover:text-blue-500"
            onClick={(e) => e.stopPropagation()}
          >
            {createdAt}
          </a>
        </div>
      </div>

      {/* 推文内容 */}
      <div class="mb-3">
        <p class="text-gray-800 whitespace-pre-wrap break-words leading-relaxed">
          {legacy.full_text}
        </p>
      </div>

      {/* 媒体内容 */}
      {media.length > 0 && (
        <div class="mb-3">
          <div class={`grid gap-2 ${media.length === 1 ? 'grid-cols-1' : media.length === 2 ? 'grid-cols-2' : 'grid-cols-2'}`}>
            {media.slice(0, 4).map((item, index) => (
              <div key={item.id_str} class="relative">
                {item.type === 'photo' && (
                  <img 
                    src={item.media_url_https}
                    alt="推文图片"
                    class="w-full h-32 object-cover rounded-lg"
                    loading="lazy"
                  />
                )}
                {item.type === 'video' && (
                  <div class="relative">
                    <img 
                      src={item.media_url_https}
                      alt="视频缩略图"
                      class="w-full h-32 object-cover rounded-lg"
                      loading="lazy"
                    />
                    <div class="absolute inset-0 flex items-center justify-center">
                      <div class="bg-black bg-opacity-50 rounded-full p-2">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M8 5v10l8-5-8-5z"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                )}
                {item.type === 'animated_gif' && (
                  <div class="relative">
                    <img 
                      src={item.media_url_https}
                      alt="GIF"
                      class="w-full h-32 object-cover rounded-lg"
                      loading="lazy"
                    />
                    <div class="absolute top-2 left-2">
                      <span class="bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                        GIF
                      </span>
                    </div>
                  </div>
                )}
                {/* 如果有超过4个媒体文件，显示数量 */}
                {index === 3 && media.length > 4 && (
                  <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
                    <span class="text-white text-lg font-semibold">
                      +{media.length - 4}
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 互动数据 */}
      <div class="flex items-center justify-between text-gray-500 text-sm border-t pt-3">
        <div class="flex items-center space-x-4">
          {/* 回复数 */}
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
            </svg>
            <span>{legacy.reply_count || 0}</span>
          </div>

          {/* 转推数 */}
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            <span>{legacy.retweet_count || 0}</span>
          </div>

          {/* 点赞数 */}
          <div class={`flex items-center ${legacy.favorited ? 'text-red-500' : ''}`}>
            <svg class="w-4 h-4 mr-1" fill={legacy.favorited ? "currentColor" : "none"} stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
            </svg>
            <span>{legacy.favorite_count || 0}</span>
          </div>

          {/* 书签 */}
          {legacy.bookmarked && (
            <div class="flex items-center text-blue-500">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"/>
              </svg>
              <span>已收藏</span>
            </div>
          )}
        </div>

        {/* 查看数 */}
        {tweet.views?.count && (
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
            </svg>
            <span>{parseInt(tweet.views.count).toLocaleString()}</span>
          </div>
        )}
      </div>
    </div>
  );
}
