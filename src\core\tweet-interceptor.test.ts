import { UnifiedTweetInterceptor } from './tweet-interceptor';
import { Extension, ExtensionType } from './extensions';

// Mock dependencies
jest.mock('./database', () => ({
  db: {
    extAddTweets: jest.fn(),
  },
}));

jest.mock('../utils/logger', () => ({
  default: {
    info: jest.fn(),
    debug: jest.fn(),
    errorWithBanner: jest.fn(),
  },
}));

jest.mock('../utils/api', () => ({
  extractTimelineTweet: jest.fn(),
  isTimelineEntryTweet: jest.fn(),
  isTimelineEntryConversationThread: jest.fn(),
  isTimelineEntryProfileConversation: jest.fn(),
  isTimelineEntrySearchGrid: jest.fn(),
  isTimelineEntryProfileGrid: jest.fn(),
}));

describe('UnifiedTweetInterceptor', () => {
  let mockExtension: Extension;
  let mockRequest: any;
  let mockResponse: any;

  beforeEach(() => {
    mockExtension = {
      name: 'TestModule',
      type: ExtensionType.TWEET,
    } as Extension;

    mockRequest = {
      method: 'GET',
      url: '',
    };

    mockResponse = {
      responseText: '',
      status: 200,
    };

    jest.clearAllMocks();
  });

  it('should handle TweetDetail endpoint', () => {
    mockRequest.url = 'https://twitter.com/i/api/graphql/abc123/TweetDetail';
    mockResponse.responseText = JSON.stringify({
      data: {
        threaded_conversation_with_injections_v2: {
          instructions: [
            {
              type: 'TimelineAddEntries',
              entries: [],
            },
          ],
        },
      },
    });

    expect(() => {
      UnifiedTweetInterceptor(mockRequest, mockResponse, mockExtension);
    }).not.toThrow();
  });

  it('should handle SearchTimeline endpoint', () => {
    mockRequest.url = 'https://twitter.com/i/api/graphql/xyz789/SearchTimeline';
    mockResponse.responseText = JSON.stringify({
      data: {
        search_by_raw_query: {
          search_timeline: {
            timeline: {
              instructions: [
                {
                  type: 'TimelineAddEntries',
                  entries: [],
                },
              ],
            },
          },
        },
      },
    });

    expect(() => {
      UnifiedTweetInterceptor(mockRequest, mockResponse, mockExtension);
    }).not.toThrow();
  });

  it('should ignore non-matching URLs', () => {
    mockRequest.url = 'https://twitter.com/i/api/some-other-endpoint';
    
    UnifiedTweetInterceptor(mockRequest, mockResponse, mockExtension);
    
    // Should not process anything for non-matching URLs
    expect(JSON.parse).not.toHaveBeenCalled();
  });

  it('should handle malformed JSON gracefully', () => {
    mockRequest.url = 'https://twitter.com/i/api/graphql/abc123/TweetDetail';
    mockResponse.responseText = 'invalid json';

    expect(() => {
      UnifiedTweetInterceptor(mockRequest, mockResponse, mockExtension);
    }).not.toThrow();
  });
});
