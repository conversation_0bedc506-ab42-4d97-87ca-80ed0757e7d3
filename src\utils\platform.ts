/**
 * Platform compatibility layer for userscript vs Chrome extension
 */

// Check if we're running as a Chrome extension (injected script context)
export const isExtension = typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;

// Check if we're running as a userscript
export const isUserScript = typeof GM_registerMenuCommand !== 'undefined';

// Check if we're in the injected script context (page context)
export const isInjectedScript =
  isExtension && typeof window !== 'undefined' && window === window.top;

/**
 * Register a menu command (userscript) or do nothing (extension)
 */
export function registerMenuCommand(text: string, callback: () => void): void {
  if (isUserScript && typeof GM_registerMenuCommand !== 'undefined') {
    GM_registerMenuCommand(text, callback);
  }
  // For Chrome extension, menu commands are handled by the popup
}

/**
 * Get the global window object with XMLHttpRequest
 */
export function getGlobalWindow(): Window & { XMLHttpRequest: typeof XMLHttpRequest } {
  if (isUserScript && typeof unsafeWindow !== 'undefined') {
    return unsafeWindow as Window & { XMLHttpRequest: typeof XMLHttpRequest };
  }
  // For Chrome extension injected script, we're already in the page context
  return window as Window & { XMLHttpRequest: typeof XMLHttpRequest };
}

/**
 * Open a URL in a new tab
 */
export function openUrl(url: string): void {
  if (isExtension && chrome.tabs) {
    chrome.tabs.create({ url });
  } else {
    window.open(url, '_blank');
  }
}
